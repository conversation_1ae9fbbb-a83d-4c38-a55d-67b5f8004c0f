import React from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  // ModalCloseButton,
  Box,
  Flex,
  Text,
  IconButton,
  Spinner,
  useColorModeValue
} from 'common/components';
import { CloseIcon } from 'assets/svg';
import colors from 'theme/foundations/colors';

const PopupModal = ({
  isOpen = false,
  onClose = () => {},
  title = '',
  children,
  size = '6xl',
  loading = false,
  headerProps = {},
  bodyProps = {},
  contentProps = {},
  overlayProps = {},
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEsc = true,
  isCentered = true,
  scrollBehavior = 'inside',
  ...rest
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const bodyBg = useColorModeValue('gray.50', 'gray.900');

  const handleClose = () => {
    if (loading) return; // Prevent closing while loading
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size={size}
      isCentered={isCentered}
      closeOnOverlayClick={closeOnOverlayClick && !loading}
      closeOnEsc={closeOnEsc && !loading}
      scrollBehavior={scrollBehavior}
      {...rest}
    >
      <ModalOverlay
        bg="blackAlpha.600"
        backdropFilter="blur(4px)"
        {...overlayProps}
      />
      <ModalContent
        maxH="90vh"
        overflow="hidden"
        borderRadius="xl"
        boxShadow="2xl"
        {...contentProps}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <ModalHeader
            bg={headerBg}
            borderBottom="1px solid"
            borderColor="gray.200"
            py={4}
            px={6}
            {...headerProps}
          >
            <Flex align="center" justify="space-between">
              {title && (
                <Text
                  fontSize="xl"
                  fontWeight="semibold"
                  color="gray.800"
                  flex="1"
                  pr={4}
                >
                  {title}
                </Text>
              )}

              {showCloseButton && (
                <IconButton
                  icon={<CloseIcon boxSize={4} />}
                  aria-label="Close modal"
                  size="sm"
                  variant="ghost"
                  borderRadius="full"
                  color="gray.500"
                  _hover={{
                    bg: 'gray.100',
                    color: 'gray.700'
                  }}
                  onClick={handleClose}
                  isDisabled={loading}
                />
              )}
            </Flex>
          </ModalHeader>
        )}

        {/* Body */}
        <ModalBody
          bg={bodyBg}
          p={0}
          overflow="auto"
          {...bodyProps}
        >
          {loading ? (
            <Flex
              direction="column"
              align="center"
              justify="center"
              minH="400px"
              gap={4}
            >
              <Spinner
                size="xl"
                color={colors.primary[500]}
                thickness="4px"
              />
              <Text
                fontSize="md"
                color="gray.600"
                fontWeight="medium"
              >
                Loading application details...
              </Text>
            </Flex>
          ) : (
            <Box>
              {children}
            </Box>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default PopupModal;

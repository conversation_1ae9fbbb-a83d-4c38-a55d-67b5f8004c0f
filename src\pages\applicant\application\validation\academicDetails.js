import * as yup from 'yup';
import { t } from 'common/components';

export const academicDetailsSchema = yup.object().shape({
  // Common fields for all scholarship types
  // Class 10 Board Exam Details
  board: yup.string().required(t('fieldRequired', { field: t('board') })),
  gradePercentage: yup
    .string()
    .required(t('fieldRequired', { field: t('gradePercentage') }))
    .matches(/^[0-9.]+$/, t('onlyNumbersAndDecimalAllowed'))
    .test(
      'is-valid-percentage',
      t('isInvalid', { type: t('gradePercentage') }),
      (value) => parseFloat(value) >= 0 && parseFloat(value) <= 100
    ),
  institutionName: yup
    .string()
    .required(t('fieldRequired', { field: t('institutionName') }))
    .min(5, t('fieldMinLength', { field: t('institutionName'), min: t('5') }))
    .max(100, t('fieldMaxLength', { field: t('institutionName'), min: t('100') })),
  institutionType: yup.string().required(t('fieldRequired', { field: t('institutionType') })),
  institutionLocation: yup
    .string()
    .required(t('fieldRequired', { field: t('institutionLocation') }))
    .min(5, t('fieldMinLength', { field: t('institutionLocation'), min: t('5') }))
    .max(100, t('fieldMaxLength', { field: t('institutionLocation'), min: t('100') })),
  yearOfCompletion: yup
    .string().required(t('fieldRequired', { field: t('yearOfCompletion') })),
  stateOfInstitution: yup
    .string().required(t('fieldRequired', { field: t('stateOfInstitution') })),
  districtOfInstitution: yup
    .string().required(t('fieldRequired', { field: t('districtOfInstitution') })),

  // Current Course Details
  currentInstitutionType: yup
    .string().required(t('fieldRequired', { field: t('currentInstitutionType') })),
  currentInstitutionName: yup
    .string().required(t('fieldRequired', { field: t('institutionName') }))
    .min(5, t('fieldMinLength', { field: t('institutionName'), min: t('5') }))
    .max(100, t('fieldMaxLength', { field: t('institutionName'), min: t('100') })),
  courseMode: yup
    .string().required(t('fieldRequired', { field: t('courseMode') })),
  academicYear: yup
    .string().required(t('fieldRequired', { field: t('academicYear') })),
  dateOfAdmission: yup
    .date()
    .required(t('dateOfAdmissionRequired'))
    .max(new Date(), t('dateCannotBeFuture')),
  registerNumber: yup
    .string()
    .when('$scholarshipType', {
      is: '10th',
      then: yup.string().required(t('registerNumberRequired'))
    }),
  grade: yup
    .string()
    .when('$scholarshipType', {
      is: '10th',
      then: yup.string().required(t('gradeRequired'))
    }),
  marksObtained: yup
    .string()
    .when('$scholarshipType', {
      is: '10th',
      then: yup.string()
        .required(t('marksObtainedRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  totalMarks: yup
    .string()
    .when('$scholarshipType', {
      is: '10th',
      then: yup.string()
        .required(t('totalMarksRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  percentage: yup
    .string()
    .when('$scholarshipType', {
      is: '10th',
      then: yup.string()
        .required(t('percentageRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
        .test(
          'is-valid-percentage',
          t('validPercentageRequired'),
          (value) => parseFloat(value) >= 0 && parseFloat(value) <= 100
        )
    }),

  // Higher Secondary specific fields
  hsBoard: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('boardRequired'))
    }),
  hsInstitutionName: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string()
        .required(t('institutionNameRequired'))
        .min(3, t('min3Characters'))
        .max(100, t('max100Characters'))
    }),
  hsInstitutionType: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('institutionTypeRequired'))
    }),
  hsInstitutionLocation: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string()
        .required(t('institutionLocationRequired'))
        .min(3, t('min3Characters'))
        .max(100, t('max100Characters'))
    }),
  hsYearOfCompletion: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('yearOfCompletionRequired'))
    }),
  hsStateOfInstitution: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('stateOfInstitutionRequired'))
    }),
  hsDistrictOfInstitution: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('districtOfInstitutionRequired'))
    }),
  hsRegisterNumber: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('registerNumberRequired'))
    }),
  hsGrade: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string().required(t('gradeRequired'))
    }),
  hsMarksObtained: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string()
        .required(t('marksObtainedRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  hsTotalMarks: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string()
        .required(t('totalMarksRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  hsPercentage: yup
    .string()
    .when('$scholarshipType', {
      is: 'higherSecondary',
      then: yup.string()
        .required(t('percentageRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
        .test(
          'is-valid-percentage',
          t('validPercentageRequired'),
          (value) => parseFloat(value) >= 0 && parseFloat(value) <= 100
        )
    }),

  // Undergraduate specific fields
  underGradCourseName: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string()
        .required(t('courseNameRequired'))
        .min(3, t('min3Characters'))
        .max(100, t('max100Characters'))
    }),
  instructionName: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string()
        .required(t('institutionNameRequired'))
        .min(3, t('min3Characters'))
        .max(100, t('max100Characters'))
    }),
  instructionType: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('institutionTypeRequired'))
    }),
  instructionLocation: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('institutionLocationRequired'))
    }),
  underGradYearOfCompletion: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('yearOfCompletionRequired'))
    }),
  underGradStateOfInstitution: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('stateOfInstitutionRequired'))
    }),
  underGradDistrictOfInstitution: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('districtOfInstitutionRequired'))
    }),
  competitiveExam: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string()
        .required(t('competitiveExamNameRequired'))
        .min(3, t('min3Characters'))
        .max(100, t('max100Characters'))
    }),
  underGradGradingSystem: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string().required(t('gradingSystemRequired'))
    }),
  underGradMarksOrScore: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string()
        .required(t('marksOrScoreRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  underGradTotalMarks: yup
    .string()
    .when(['$scholarshipType', 'underGradGradingSystem'], {
      is: (scholarshipType, gradingSystem) => scholarshipType === 'undergraduate' && gradingSystem === 'mark',
      then: yup.string()
        .required(t('totalMarksRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
    }),
  underGradPercentage: yup
    .string()
    .when('$scholarshipType', {
      is: 'undergraduate',
      then: yup.string()
        .required(t('percentageRequired'))
        .matches(/^\d*\.?\d+$/, t('onlyNumbersAndDecimalAllowed'))
        .test(
          'is-valid-percentage',
          t('validPercentageRequired'),
          (value) => parseFloat(value) >= 0 && parseFloat(value) <= 100
        )
    })
});

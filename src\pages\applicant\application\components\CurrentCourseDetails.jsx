import {
  Box, FormController, Grid, GridItem, t, TitledCard
} from 'common/components';
import { FormLabel } from 'common/custom-components';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { ACADEMIC_YEAR_OPTIONS, COURSE_MODE, CURRENT_INSTITUTION_TYPE } from '../constants';

const CurrentCourseDetails = () => {
  const { control, formState: { errors } } = useFormContext();

  return (
    <TitledCard title={t('currentCourseDetails')} mt={8}>
      <Box p={6}>
        <Box mb={6}>
          <FormLabel label={t('institutionType')} required />
          <Box mt={2} mb={8} fontSize="sm" color="gray.500" />
          <FormController
            type="select"
            name="currentInstitutionType"
            control={control}
            errors={errors}
            options={CURRENT_INSTITUTION_TYPE}
            optionKey="name"
            placeholder={t('currentInstitutionType')}
            label={t('pleaseSelectOption')}
            required
          />
        </Box>

        <Box mb={6}>
          <FormController
            type="text"
            label={t('institutionName')}
            name="currentInstitutionName"
            control={control}
            errors={errors}
            placeholder={t('enterInstitutionName')}
            required
          />
        </Box>

        <Box mb={6}>
          <FormLabel label={t('courseMode')} required />
          <Box mt={2} mb={2} fontSize="sm" color="gray.500">
            {t('pleaseSelectOption')}
          </Box>
          <FormController
            type="radio"
            name="courseMode"
            control={control}
            errors={errors}
            options={COURSE_MODE}
            optionKey="code"
            required
            direction="row"
          />
        </Box>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6]}>
            <FormController
              type="select"
              label={t('academicYear')}
              name="academicYear"
              control={control}
              errors={errors}
              options={ACADEMIC_YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6]}>
            <FormController
              type="date"
              label={t('dateOfAdmission')}
              name="dateOfAdmission"
              control={control}
              errors={errors}
              required
              placeholder="DD/MM/YYYY"
              max={new Date().toISOString().split('T')[0]}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default CurrentCourseDetails;

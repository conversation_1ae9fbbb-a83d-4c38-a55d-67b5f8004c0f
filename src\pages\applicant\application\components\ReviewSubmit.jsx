import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  DocumentPreviewSection
} from 'common/components';
import {
  AccordionComponent, PreviewSection, StepperButtons, Alert as CustomAlert
} from 'common/custom-components';
import { t } from 'i18next';
import { useSelector, useDispatch } from 'react-redux';
import { getScholarshipType } from 'pages/common/selectors';
import colors from 'theme/foundations/colors';
import { actions as commonActions } from 'pages/common/slice';
import { STEPPER_STEPS } from '../constants';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

const ReviewSubmit = ({
  onPrevious, onEdit, applicationDetails, isDetailsSuccess
}) => {
  const scholarshipType = useSelector(getScholarshipType);
  const dispatch = useDispatch();

  // const [submitApplication, { isLoading: isSubmitting }] = useSubmitApplicationMutation();

  const [showAlert, setShowAlert] = useState(false);

  const getScholarshipTypeName = () => {
    if (scholarshipType) {
      return t(scholarshipType);
    }
    return null;
  };

  const getConfirmationMessage = () => {
    const scholarshipName = getScholarshipTypeName();
    return t('submitApplicationConfirmMessage', { scholarshipType: scholarshipName });
  };

  const getAcademicDetailsData = () => {
    return [
      ...getPreviousAcademicDetailsData(applicationDetails, isDetailsSuccess),
      ...getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess)
    ];
  };

  const handleEdit = (section) => {
    if (onEdit) onEdit(section);
  };

  const handleSubmitClick = () => {
    setShowAlert(true);
  };

  const handleAlertClose = () => {
    setShowAlert(false);
  };

  const handleSubmitConfirm = () => {
    // TODO: commented for future use
    // submitApplication({
    //   applicationId,
    //   closeAlert: () => setShowAlert(false)
    // });

    //  TODO: Remove once API is ready
    setShowAlert(false);
    dispatch(
      commonActions.setCustomToast({
        open: true,
        variant: 'success',
        message: 'Application created successfully',
        title: 'Success'
      })
    );
    dispatch(commonActions.navigateTo({
      to: 'ui/applicant/dashboard',
      replace: true
    }));
  };

  // Accordion data with preview sections
  const accordionData = [
    {
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      onClick: () => handleEdit(STEPPER_STEPS.APPLICANT_DETAILS),
      isCompleted: false
    },
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      onClick: () => handleEdit(STEPPER_STEPS.PARENT_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      onClick: () => handleEdit(STEPPER_STEPS.BANK_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      onClick: () => handleEdit(STEPPER_STEPS.ACADEMIC_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails.studentDocumentsDetails ? [{
      title: t('documentUpload'),
      content: (
        <DocumentPreviewSection documents={[]} />
      ),
      id: 5,
      onClick: () => handleEdit(STEPPER_STEPS.DOCUMENTS_UPLOAD),
      isCompleted: false
    }] : [])
  ];

  return (
    <VStack spacing={4} align="stretch">
      {/* Header */}
      <Box alignContent="center" maxWidth={{ base: 'sm', md: 'md', lg: 'lg' }} mx="auto">
        <Text
          fontSize={{ base: 'sm', md: 'lg' }}
          fontWeight="semibold"
          color="white"
          alignItems="center"
          textAlign="center"
          p={2}
          borderRadius="md"
          bg={colors.primary[500]}
        >
          {t('reviewSubmit')}
        </Text>
      </Box>

      {/* Accordion with Preview Sections */}
      <AccordionComponent
        data={accordionData}
        allowMultiple
        currentIndexes={accordionData.map((_, index) => index)}
        isCollapsible={false}
      />

      {/* Action Buttons */}
      <StepperButtons
        currentStep={5}
        totalSteps={6}
        onNext={handleSubmitClick}
        onPrevious={onPrevious}
        layout="space-between"
      />

      {/* Submit Confirmation Alert */}
      <CustomAlert
        open={showAlert}
        close={handleAlertClose}
        variant="success"
        bodyTitle={t('confirmApplication')}
        message={getConfirmationMessage()}
        forwardActionText={t('confirm')}
        backwardActionText={t('cancel')}
        actionForward={handleSubmitConfirm}
        actionBackward={handleAlertClose}
        // actionForwardLoading={isSubmitting}
        closeOnOverlayClick={false}
        closeOnEsc={false}
      />
    </VStack>
  );
};

export default ReviewSubmit;

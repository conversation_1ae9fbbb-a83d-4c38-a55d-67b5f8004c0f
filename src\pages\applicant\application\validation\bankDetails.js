import { t } from 'common/components';
import { NUMERIC } from 'common/regex';
import * as yup from 'yup';

export const bankDetailsSchema = yup.object().shape({
  accountHolderName: yup
    .string()
    .required(t('fieldRequired', { field: t('accountHolderName') }))
    .min(5, t('fieldMinLength', { field: t('accountHolderName'), min: t('5') }))
    .max(10, t('fieldMaxLength', { field: t('accountHolderName'), min: t('10') })),
  accountNumber: yup
    .string()
    .required(t('fieldRequired', { field: t('accountNumber') }))
    .matches(NUMERIC, t('onlyNumbersAllowed'))
    .min(9, t('fieldMinLength', { field: t('accountNumber'), min: t('9') }))
    .max(18, t('fieldMaxLength', { field: t('accountNumber'), min: t('18') })),
  bankName: yup
    .string()
    .required(t('bankNameRequired')),
  branchName: yup
    .string()
    .required(t('branchRequired')),
  ifscCode: yup
    .string()
    .required(t('ifscRequired'))
    .matches(/^[A-Za-z]{4}0[A-Za-z0-9]{6}$/, t('invalidIFSCFormat'))
});

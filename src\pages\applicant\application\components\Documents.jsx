import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box, TitledCard, CustomAlert, t, FormController, Grid,
  CircularProgress
} from 'common/components';
import { GuideLines } from 'assets/svg';
import { FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';
import { getFileContent } from 'pages/others/fileDownload/helpers';
import { StepperButtons } from 'common/custom-components';
import { useParams } from 'react-router-dom';
import { documentSchema } from '../validation/document';
import { useGetRequiredDocumentsQuery } from '../api';
import { DOCUMENT_META_DATA } from '../constants';

const Documents = ({
  isHSSApplicant = false,
  isPGApplicant = false,
  hasSpecialStatus = false,
  onNext,
  onPrevious
}) => {
  const { applicationId } = useParams();
  const {
    control, setValue, handleSubmit, formState: { errors }
  } = useForm({
    mode: 'all',
    resolver: yupResolver(documentSchema),
    defaultValues: {
      isHSSApplicant,
      isPGApplicant,
      hasSpecialStatus
    }
  });

  const {
    data: { payload: { attachments = [] } = {} } = {},
    isLoading, isError
  } = useGetRequiredDocumentsQuery(applicationId);

  const [previewUrls, setPreviewUrls] = useState({
    aadhaar: [],
    passport: [],
    marksheet: [],
    ugMarksheet: [],
    scoreCard: [],
    incomeCertificate: [],
    specialCertificate: [],
    achievementCertificate: [],
    bankPassbook: [],
    rationCard: []
  });

  const handleFileUpload = (file, documentType) => {
    if (!file) return;
    setValue(documentType, file);
    if (file.size < FILE_SIZE && VALID_FILE_TYPE.includes(file?.type)) {
      setPreviewUrls((prev) => ({ ...prev, [documentType]: [getFileContent(file)] }));
    }
  };

  const handleRemoveFile = (documentType) => {
    setValue(documentType, null);
    if (previewUrls[documentType]) {
      URL.revokeObjectURL(previewUrls[documentType]);
      setPreviewUrls((prev) => ({ ...prev, [documentType]: [] }));
    }
  };

  const formSubmit = (data) => {
    if (onNext) {
      onNext(data);
    }
  };

  return (
    <form onSubmit={handleSubmit(formSubmit)}>
      <TitledCard title={t('documentUpload')}>
        <Box p={6}>
          <Grid templateColumns="repeat(1, 1fr)" gap={4}>
            <Box mb={9}>
              <CustomAlert
                label={t('documentGuidelinesTitle')}
                message={t('documentGuidelinesMessage')}
                bg="orange.50"
                iconColor="orange.500"
                textColor="orange.700"
                icon={GuideLines}
              />
            </Box>
            {isLoading && <CircularProgress />}

            {!isLoading && !isError && attachments.map((key) => {
              const meta = DOCUMENT_META_DATA[key];
              if (!meta) return null;
              return (
                <FormController
                  key={meta.name}
                  showPreview
                  type="file"
                  name={meta.name}
                  label={t(meta.label)}
                  required
                  description={t(meta.description)}
                  previewData={previewUrls[meta.name]}
                  errors={errors}
                  handleChange={(file) => handleFileUpload(file, meta.name)}
                  onFileRemove={() => handleRemoveFile(meta.name)}
                  control={control}
                />
              );
            })}
          </Grid>
        </Box>
      </TitledCard>
      {/* Action Buttons */}
      <Box mt={6}>
        <StepperButtons
          currentStep={4}
          totalSteps={6}
          onNext={onNext}
          onPrevious={onPrevious}
          layout="space-between"
        />
      </Box>
    </form>
  );
};

export default Documents;

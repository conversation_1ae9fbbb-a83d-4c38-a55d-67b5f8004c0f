export const BASE_API_URL = 'norka-scholarship-service';
export const BASE_AADHAAR_URL = 'aadhaar-services';

export const API_URL = {
  LOGIN: {
    VERIFY_LOGIN: `${BASE_API_URL}/api/auth/login-userDetails`,
    REGISTER: `${BASE_API_URL}/v1/scholarship/create-user`,
    RESET_PASSWORD: `${BASE_API_URL}/v1/user/reset-password`,
    SEND_EMAIL_DETAILS: `${BASE_API_URL}/v1/password-reset/email-send`,
    SEND_EMAIL_RESET_PASSWORD: `${BASE_API_URL}/v1/user/forgot-password`,
    VERIFY_OTP: `${BASE_API_URL}/v1/scholarship/verify-otp`,
    RESEND_OTP: `${BASE_API_URL}/v1/scholarship/send-otp`,
    FORGOT_PASSWORD: `${BASE_API_URL}/v1/scholarship/forgot-password`
  },
  OTP: {
    SEND_MOBILE_OTP: `${BASE_API_URL}/v1/otp/send-mobile`,
    SEND_EMAIL_OTP: `${BASE_API_URL}/v1/otp/send-email`,
    SEND_AADHAAR_OTP: `${BASE_AADHAAR_URL}/v2/generate-otp`,
    VERIFY_OTP: `${BASE_AADHAAR_URL}/v2/otp-kyc`
  },
  SAMPLE: {
    COUNTRY: 'egov-mdms-service/v1/common-masters/countries',
    GENDER: 'egov-mdms-service/v1/common-masters/genders',
    BIRTH_PDF: 'birth-services/cr/e-register/download',
    SIGN_PDF: 'signpdf',
    LOCAL_ENROLLMENT: 'dssignature'
  },
  COMMON: {
    GENDER: `${BASE_API_URL}/v1/scholarship/get-gender`,
    STATE: `${BASE_API_URL}/v1/scholarship/get-state-by-id/{stateId}`,
    ALL_STATE: `${BASE_API_URL}/v1/scholarship/get-all-states`,
    DISTRICT: `${BASE_API_URL}/v1/scholarship/get-district-by-state/{stateId}`,
    EDUCATION_QUALIFICATIONS: `${BASE_API_URL}/v1/scholarship/get-education-qualifications`,
    GET_BOARD: `${BASE_API_URL}/v1/scholarship/get-board`
  },
  APPLICATION: {
    GET_APPLICATIONS: `${BASE_API_URL}/v1/scholarship/get-applications`,
    SAVE_PERSONAL_DETAILS: `${BASE_API_URL}/v1/scholarship/save-personal-details`,
    SAVE_BANK_DETAILS: `${BASE_API_URL}/v1/scholarship/save-bank-details`,
    BANK_DETAILS: `${BASE_API_URL}/v1/scholarship/save-bank-details`,
    SAVE_ACADEMIC_DETAILS: `${BASE_API_URL}/v1/scholarship/save-academic-details`,
    ACADEMIC_DETAILS: `${BASE_API_URL}/v1/scholarship/get-academic-details`,
    UPDATE_PERSONAL_DETAILS: `${BASE_API_URL}/v1/scholarship/update-personal-details/{id}`,
    SAVE_PARENT_DETAILS: `${BASE_API_URL}/v1/scholarship/save-parent-guardian-details`,
    UPDATE_PARENT_DETAILS: `${BASE_API_URL}/v1/scholarship/update-parent-guardian-details/{id}`,
    GET_APPLICATION_DETAILS: `${BASE_API_URL}/v1/scholarship/get-application-details/{id}`,
    GET_BANK_DETAILS: `${BASE_API_URL}/v1/scholarship/get-bank-details`,
    UPDATE_BANK_DETAILS: `${BASE_API_URL}/v1/scholarship/update-bank-details/{id}`,
    UPLOAD_FILE: '/v1/files/upload',
    UPDATE_ACADEMIC_DETAILS: `${BASE_API_URL}/v1/scholarship/update-academic-details`,
    UNIVERSITIES: `${BASE_API_URL}/v1/scholarship/get-universities/{stateId}`,
    SUBMIT_APPLICATION: `${BASE_API_URL}/v1/scholarship/submit-application/{id}`,
    REQUIRED_DOCUMENTS: `${BASE_API_URL}/v1/scholarship/fetch-required-documents/{id}`
  }
};

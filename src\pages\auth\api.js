import { createApi } from '@reduxjs/toolkit/query/react';
import { API_URL, ROUTE_URL } from 'common';
import { _ } from 'utils/lodash';
import { getBaseQuery, handleAPIRequest } from 'utils/http';
import { actions as commonActions } from 'pages/common/slice';
import { STORAGE_KEYS } from 'utils/constants';
import { setDataToStorage } from 'utils/encryption';
import { APPLICANT_DASHBOARD_ROUTE } from './constant';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: getBaseQuery(),
  tagTypes: ['register'],
  endpoints: (builder) => ({
    registerUser: builder.mutation({
      invalidatesTags: ['register'],
      query: (data) => ({
        url: API_URL.LOGIN.REGISTER,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { otpResponse } = {} } = {} } = result;
          if (!_.isEmpty(otpResponse)) {
            dispatch(commonActions.navigateTo({
              to: ROUTE_URL.AUTH.FULL_PATHS.OTP,
              options: {
                state: {
                  id: otpResponse.otp.UUID,
                  mobileNumber: _arg.mobileNumber,
                  emailId: _arg.emailId
                },
                replace: false
              },
              isSameModule: true
            }));
          }
        }
      })
    }),
    verifyOTP: builder.mutation({
      query: ({ id, otp, mobileNumber }) => ({
        url: `${API_URL.LOGIN.VERIFY_OTP}?id=${id}&otp=${otp}&mobileNo=${mobileNumber}`,
        method: 'POST'
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, _arg, dispatch) => {
          dispatch(commonActions.navigateTo({
            to: ROUTE_URL.AUTH.FULL_PATHS.LOGIN,
            replace: true
          }));
        }
      })
    }),
    resendOTP: builder.mutation({
      query: (mobileNumber) => ({
        url: API_URL.LOGIN.RESEND_OTP,
        method: 'POST',
        params: {
          mobileNo: mobileNumber
        }
      })
    }),
    loginUser: builder.mutation({
      query: (credentials) => ({
        url: API_URL.LOGIN.VERIFY_LOGIN,
        method: 'POST',
        body: credentials
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { token, userDataResponse } = {} } = {} } = result;
          if (token) {
            localStorage.setItem('token', token);

            if (userDataResponse) {
              setDataToStorage(STORAGE_KEYS.USER_DETAILS, userDataResponse, true);
            }

            dispatch(commonActions.navigateTo({
              to: APPLICANT_DASHBOARD_ROUTE,
              replace: true
            }));
          }
        }
      })
    }),
    verifyToken: builder.query({
      query: () => API_URL.VERIFY_TOKEN
    }),
    forgotPassword: builder.mutation({
      query: (credentials) => ({
        url: API_URL.LOGIN.FORGOT_PASSWORD,
        method: 'POST',
        body: credentials
      })
    }),
    resetPassword: builder.mutation({
      query: (data) => ({
        url: API_URL.RESET_PASSWORD,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, _arg, dispatch) => {
          dispatch(commonActions.navigateTo({
            to: ROUTE_URL.AUTH.FULL_PATHS.LOGIN,
            replace: true
          }));
        }
      })
    }),
    logoutUser: builder.mutation({
      queryFn: async () => {
        localStorage.clear();
        window.location.href = '/ui/auth/login';
        return { data: { success: true } };
      }
    })
  })
});

export const {
  useRegisterUserMutation,
  useLoginUserMutation,
  useVerifyTokenQuery,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyOTPMutation,
  useResendOTPMutation,
  useLogoutUserMutation
} = authApi;
